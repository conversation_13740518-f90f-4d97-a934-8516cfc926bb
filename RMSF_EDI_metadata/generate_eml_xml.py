import xml.etree.ElementTree as ET
import xml.dom.minidom as minidom
import os
from datetime import datetime

# Read metadata templates
def read_template(filename):
    path = os.path.join("metadata_templates", filename)
    with open(path, 'r') as f:
        return f.read().strip()

def read_table_template(filename):
    path = os.path.join("metadata_templates", filename)
    data = []
    with open(path, 'r') as f:
        lines = f.readlines()
        headers = lines[0].strip().split('\t')
        for line in lines[1:]:
            if line.strip():
                values = line.strip().split('\t')
                data.append(dict(zip(headers, values)))
    return data

# Create EML XML structure
def create_eml_xml():
    # Create root element with namespaces
    root = ET.Element('eml:eml', {
        'xmlns:eml': 'https://eml.ecoinformatics.org/eml-2.2.0',
        'xmlns:xsi': 'http://www.w3.org/2001/XMLSchema-instance',
        'xmlns:stmml': 'http://www.xml-cml.org/schema/schema24',
        'xsi:schemaLocation': 'https://eml.ecoinformatics.org/eml-2.2.0 https://eml.ecoinformatics.org/eml-2.2.0/eml.xsd',
        'packageId': 'edi.999.1',
        'system': 'EDI'
    })
    
    # Add access
    access = ET.SubElement(root, 'access', {'authSystem': 'https://pasta.edirepository.org/authentication', 'order': 'allowFirst'})
    allow = ET.SubElement(access, 'allow')
    ET.SubElement(allow, 'principal').text = 'uid=ahridoy,o=EDI,dc=edirepository,dc=org'
    ET.SubElement(allow, 'permission').text = 'all'
    allow2 = ET.SubElement(access, 'allow')
    ET.SubElement(allow2, 'principal').text = 'public'
    ET.SubElement(allow2, 'permission').text = 'read'
    
    # Add dataset
    dataset = ET.SubElement(root, 'dataset')
    
    # Title
    ET.SubElement(dataset, 'title').text = 'Rocky Mountain Spotted Fever Incidence and Associated Environmental Factors in Arizona Counties, 2006-2021'
    
    # Creator and other personnel
    personnel_data = read_table_template('personnel.txt')
    for person in personnel_data:
        if person.get('role') == 'creator':
            creator = ET.SubElement(dataset, 'creator')
            indiv_name = ET.SubElement(creator, 'individualName')
            ET.SubElement(indiv_name, 'givenName').text = person.get('givenName', '')
            ET.SubElement(indiv_name, 'surName').text = person.get('surName', '')
            ET.SubElement(creator, 'organizationName').text = person.get('organizationName', '')
            if person.get('electronicMailAddress'):
                ET.SubElement(creator, 'electronicMailAddress').text = person.get('electronicMailAddress')
        
        if person.get('role') == 'contact':
            contact = ET.SubElement(dataset, 'contact')
            indiv_name = ET.SubElement(contact, 'individualName')
            ET.SubElement(indiv_name, 'givenName').text = person.get('givenName', '')
            ET.SubElement(indiv_name, 'surName').text = person.get('surName', '')
            ET.SubElement(contact, 'organizationName').text = person.get('organizationName', '')
            if person.get('electronicMailAddress'):
                ET.SubElement(contact, 'electronicMailAddress').text = person.get('electronicMailAddress')
    
    # Abstract
    abstract = ET.SubElement(dataset, 'abstract')
    para = ET.SubElement(abstract, 'para')
    para.text = read_template('abstract.txt')
    
    # Keywords
    keyword_set = ET.SubElement(dataset, 'keywordSet')
    keywords = read_template('keywords.txt').split('\\n')
    for kw in keywords:
        if kw.strip():
            ET.SubElement(keyword_set, 'keyword').text = kw.strip()
    
    # Intellectual Rights
    intellectual_rights = ET.SubElement(dataset, 'intellectualRights')
    para = ET.SubElement(intellectual_rights, 'para')
    para.text = read_template('intellectual_rights.txt')
    
    # Coverage
    coverage = ET.SubElement(dataset, 'coverage')
    
    # Geographic Coverage
    geo_data = read_table_template('geographic_coverage.txt')
    if geo_data:
        geo_cov = ET.SubElement(coverage, 'geographicCoverage')
        ET.SubElement(geo_cov, 'geographicDescription').text = geo_data[0]['geographicDescription']
        bounding = ET.SubElement(geo_cov, 'boundingCoordinates')
        ET.SubElement(bounding, 'westBoundingCoordinate').text = geo_data[0]['westBoundingCoordinate']
        ET.SubElement(bounding, 'eastBoundingCoordinate').text = geo_data[0]['eastBoundingCoordinate']
        ET.SubElement(bounding, 'northBoundingCoordinate').text = geo_data[0]['northBoundingCoordinate']
        ET.SubElement(bounding, 'southBoundingCoordinate').text = geo_data[0]['southBoundingCoordinate']
    
    # Temporal Coverage
    temp_cov = ET.SubElement(coverage, 'temporalCoverage')
    range_of_dates = ET.SubElement(temp_cov, 'rangeOfDates')
    begin_date = ET.SubElement(range_of_dates, 'beginDate')
    ET.SubElement(begin_date, 'calendarDate').text = '2006-01-01'
    end_date = ET.SubElement(range_of_dates, 'endDate')
    ET.SubElement(end_date, 'calendarDate').text = '2021-12-31'
    
    # Methods
    methods = ET.SubElement(dataset, 'methods')
    method_step = ET.SubElement(methods, 'methodStep')
    description = ET.SubElement(method_step, 'description')
    para = ET.SubElement(description, 'para')
    para.text = read_template('methods.txt')
    
    # Data Table
    data_table = ET.SubElement(dataset, 'dataTable')
    ET.SubElement(data_table, 'entityName').text = 'RMSF_Arizona_data.csv'
    ET.SubElement(data_table, 'entityDescription').text = 'Daily RMSF case counts with associated climate, land cover, veterinary care access, and social vulnerability data for Arizona counties'
    
    physical = ET.SubElement(data_table, 'physical')
    ET.SubElement(physical, 'objectName').text = 'RMSF_Arizona_data.csv'
    ET.SubElement(physical, 'size', {'unit': 'bytes'}).text = str(os.path.getsize('data_objects/RMSF_Arizona_data.csv'))
    
    data_format = ET.SubElement(physical, 'dataFormat')
    text_format = ET.SubElement(data_format, 'textFormat')
    ET.SubElement(text_format, 'numHeaderLines').text = '1'
    ET.SubElement(text_format, 'recordDelimiter').text = '\\n'
    ET.SubElement(text_format, 'attributeOrientation').text = 'column'
    simple_delimited = ET.SubElement(text_format, 'simpleDelimited')
    ET.SubElement(simple_delimited, 'fieldDelimiter').text = ','
    
    distribution = ET.SubElement(physical, 'distribution')
    online = ET.SubElement(distribution, 'online')
    ET.SubElement(online, 'url').text = 'https://pasta.lternet.edu/package/data/eml/edi/999/1/RMSF_Arizona_data.csv'
    
    # Attribute List
    attribute_list = ET.SubElement(data_table, 'attributeList')
    attributes_data = read_table_template('attributes_RMSF_Arizona_data.txt')
    
    for attr in attributes_data:
        attribute = ET.SubElement(attribute_list, 'attribute')
        ET.SubElement(attribute, 'attributeName').text = attr['attributeName']
        ET.SubElement(attribute, 'attributeDefinition').text = attr['attributeDefinition']
        
        if attr['class'] == 'Date':
            storage_type = ET.SubElement(attribute, 'storageType')
            storage_type.text = 'date'
            measurement_scale = ET.SubElement(attribute, 'measurementScale')
            date_time = ET.SubElement(measurement_scale, 'dateTime')
            ET.SubElement(date_time, 'formatString').text = attr['dateTimeFormatString']
        elif attr['class'] == 'character':
            storage_type = ET.SubElement(attribute, 'storageType')
            storage_type.text = 'string'
            measurement_scale = ET.SubElement(attribute, 'measurementScale')
            nominal = ET.SubElement(measurement_scale, 'nominal')
            non_numeric_domain = ET.SubElement(nominal, 'nonNumericDomain')
            ET.SubElement(non_numeric_domain, 'textDomain')
        elif attr['class'] == 'numeric':
            storage_type = ET.SubElement(attribute, 'storageType')
            storage_type.text = 'float'
            measurement_scale = ET.SubElement(attribute, 'measurementScale')
            ratio = ET.SubElement(measurement_scale, 'ratio')
            unit = ET.SubElement(ratio, 'unit')
            if attr['unit'] in ['numberPer100000Persons', 'numberPer10000Persons']:
                ET.SubElement(unit, 'customUnit').text = attr['unit']
            else:
                ET.SubElement(unit, 'standardUnit').text = attr['unit']
            numeric_domain = ET.SubElement(ratio, 'numericDomain')
            ET.SubElement(numeric_domain, 'numberType').text = 'real'
        
        if attr.get('missingValueCode') and attr['missingValueCode'] != 'NA':
            missing_value_code = ET.SubElement(attribute, 'missingValueCode')
            ET.SubElement(missing_value_code, 'code').text = attr['missingValueCode']
            ET.SubElement(missing_value_code, 'codeExplanation').text = attr.get('missingValueCodeExplanation', '')
    
    ET.SubElement(data_table, 'numberOfRecords').text = '2496'
    
    # Custom Units
    additional_metadata = ET.SubElement(root, 'additionalMetadata')
    metadata = ET.SubElement(additional_metadata, 'metadata')
    unit_list = ET.SubElement(metadata, 'unitList', {
        'xmlns:eml': 'https://eml.ecoinformatics.org/eml-2.2.0',
        'xsi:schemaLocation': 'https://eml.ecoinformatics.org/eml-2.2.0 ../../../eml.xsd'
    })
    
    custom_units_data = read_table_template('custom_units.txt')
    for cu in custom_units_data:
        unit = ET.SubElement(unit_list, 'unit', {'id': cu['id']})
        ET.SubElement(unit, 'description').text = cu['description']
    
    return root

# Pretty print XML
def prettify_xml(elem):
    rough_string = ET.tostring(elem, encoding='unicode')
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")

# Generate the XML
print("Generating EML XML file...")
eml_root = create_eml_xml()
xml_string = prettify_xml(eml_root)

# Save to file
output_path = "eml/edi.999.1.xml"
os.makedirs("eml", exist_ok=True)

with open(output_path, 'w', encoding='utf-8') as f:
    f.write(xml_string)

print(f"✓ EML XML file successfully generated: {output_path}")
print(f"✓ File size: {os.path.getsize(output_path):,} bytes")
print("\\nThe XML file is ready for submission to EDI!")
print("\\nNext steps:")
print("1. Have Daniel review the generated XML file")
print("2. Get an official package ID from EDI to replace 'edi.999.1'")
print("3. Upload to EDI's staging environment for validation")