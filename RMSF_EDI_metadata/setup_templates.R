# Setup script to generate metadata templates
# This should be run first if you need to generate the template files

library(EMLassemblyline)

# Set working directory
setwd("/Users/<USER>/Desktop/projects/yan/RMSF_EDI_metadata")

# Generate core metadata templates
template_core_metadata(
    path = "./metadata_templates",
    license = "CCBY"
)

# Generate table attributes template
template_table_attributes(
    path = "./metadata_templates",
    data.path = "./data_objects",
    data.table = "RMSF_Arizona_data.csv"
)

# Generate geographic coverage template (if you have lat/lon columns)
# template_geographic_coverage(
#     path = "./metadata_templates",
#     data.path = "./data_objects",
#     data.table = "RMSF_Arizona_data.csv",
#     lat.col = "latitude",
#     lon.col = "longitude",
#     site.col = "County"
# )

print("Template files generated successfully!")
print("Please fill out the templates in the ./metadata_templates directory")