# Create EML metadata for RMSF Arizona dataset
# Install EMLassemblyline if not already installed:
# remotes::install_github("EDIorg/EMLassemblyline")

library(EMLassemblyline)

# Set working directory
setwd("/Users/<USER>/Desktop/projects/yan/RMSF_EDI_metadata")

# Generate the EML metadata file
make_eml(
    path = "./metadata_templates",
    data.path = "./data_objects",
    eml.path = "./eml",
    dataset.title = "Rocky Mountain Spotted Fever Incidence and Associated Environmental Factors in Arizona Counties, 2006-2021",
    temporal.coverage = c('2006-01-01', '2021-12-31'),
    geographic.description = "Counties in Arizona, United States with reported RMSF cases",
    geographic.coordinates = c(37.0, -114.0, 31.0, -109.0),  # N, W, S, E
    maintenance.description = "completed",
    data.table = 'RMSF_Arizona_data.csv',
    data.table.name = 'Rocky Mountain Spotted Fever Case and Environmental Data',
    data.table.description = 'Daily RMSF case counts with associated climate, land cover, veterinary care access, and social vulnerability data for Arizona counties',
    other.entity = NULL,
    other.entity.name = NULL,
    other.entity.description = NULL,
    user.id = 'ahridoy',  # Update with your EDI user ID when assigned
    user.domain = 'EDI',
    package.id = 'edi.999.1'  # This will be assigned by EDI
)

print("EML metadata file created successfully!")
print("Check the ./eml directory for the generated XML file")