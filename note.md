I think you can focus on the EDI task discussed tomorrow, which is a priority for DMAC because it was requested by PI and important for the center renewal.

 

I am resending the files shared by <PERSON>.

 

Pkg_492 are the completed metadata templates, R code, and final XML for the data package that is published here:

 

https://portal.edirepository.org/nis/mapbrowse?packageid=edi.492.1

 

 

below are some notes taken from yesterday:

 

https://portal.edirepository.org/nis/home.jsp

 

sign in –talk to mark about getting account

 

data example: https://portal.edirepository.org/nis/mapbrowse?packageid=edi.523.1

 

fill out the Dataset Description Worksheet together with PIs

then it helps with the metadata template

refer to the attachment _ metadata template, filled in manually for specific datasets based on the Dataset Description Worksheet. Then generate metadata with EMLassemblyline in R

 

https://github.com/EDIorg/EMLassemblyline/tree/main/vignettes

 

remotes::install_github("EDIorg/EMLassemblyline")

 

 

To start, you can use the rocky mountain spotted fever publication datasets to practice, which you are familiar with data and all information and can easily fill out the Dataset Description Worksheet.  Then try manually generate the metadata templates and convert into EDI metadata file. You might also refer to EDI github as well.

 

Hope you can work on this in the next two weeks and produce the metadata. Then we can ask <PERSON> to check if it was generated correctly. We are also waiting to hear from UNM EDI contact to give us access to EDI, but the metadata file is the key, which doesn’t need access to EDI first.

 

Let me know if this is clear to you and feel free to let me know if you have any questions.

 

Best,

 

Yan

Hi Yan,

 

Thank you for sharing this! That all looks like a good starting point to me. Al, here is how I recommend proceeding:

 

Get familiar with the metadata templates in the `pkg_429` folder, and compare them to the metadata and data tables in the EDI repository (https://portal.edirepository.org/nis/mapbrowse?packageid=edi.492.1)
Using your TickBase data, clean up the data table and work on filling out the metadata templates to describe it.
Get the EMLAssemblyLine R package loaded on your computer. You will need devtools to load directly from Github, since the CRAN version is out of date:
 

install.packages('devtools')

remotes::install_github("EDIorg/EMLassemblyline")

 

You will likely encounter errors here – if so, we can meet and work through them.
Once the data package is finished, we will use my EDI account to run the QAQC checks/upload to the repository.
 

Best,

 

Daniel\