# EDI Metadata Creation Guide for RMSF Data

## Overview
This guide will help you create EDI-compliant metadata for your Rocky Mountain Spotted Fever (RMSF) dataset using the EMLassemblyline R package.

## What You Need to Do

### 1. Install R and Required Packages
First, install R from https://cran.r-project.org/

Then install the required packages:
```r
# Install devtools first
install.packages('devtools')

# Install EMLassemblyline from GitHub (not CRAN, as it's outdated)
remotes::install_github("EDIorg/EMLassemblyline")

# Load the library
library(EMLassemblyline)
```

### 2. Understand Your RMSF Data
Based on the paper you have, your RMSF dataset likely includes:
- **Temporal data**: Monthly RMSF case counts (2006-2021)
- **Geographic data**: County-level data for Arizona
- **Variables measured**:
  - RMSF case counts
  - Climate variables (temperature, precipitation, relative humidity)
  - Land cover data (forest, shrubland percentages)
  - Veterinary care access
  - Dog population data

### 3. Prepare Your Data Files
Create a clean CSV file with your RMSF data including:
- County name
- Date (YYYY-MM-DD format)
- RMSF case count
- Temperature (LST)
- Precipitation
- Relative humidity
- Forest coverage (%)
- Shrubland coverage (%)
- Veterinary care access index
- Dog population

Save this as `RMSF_Arizona_2006_2021.csv` (or similar name)

### 4. Create Directory Structure
```r
# Create the project structure
template_directories(path = "~/Desktop/projects/yan", dir.name = "RMSF_metadata_project")
```

This creates:
```
RMSF_metadata_project/
├── metadata_templates/
├── data_objects/
└── eml/
```

### 5. Copy Your Data File
Place your cleaned RMSF data CSV file in the `data_objects/` folder

### 6. Generate Metadata Templates
```r
# Set working directory
setwd("~/Desktop/projects/yan/RMSF_metadata_project")

# Create core metadata templates
template_core_metadata(
    path = "./metadata_templates",
    license = "CC0"  # or "CCBY" depending on your preference
)

# Create table attributes template for your data
template_table_attributes(
    path = "./metadata_templates",
    data.path = "./data_objects",
    data.table = "RMSF_Arizona_2006_2021.csv"  # your actual filename
)

# Create geographic coverage template
template_geographic_coverage(
    path = "./metadata_templates",
    data.path = "./data_objects",
    data.table = "RMSF_Arizona_2006_2021.csv",
    lat.col = "latitude",  # if you have these columns
    lon.col = "longitude",
    site.col = "county"
)
```

### 7. Fill Out the Templates
You'll need to edit these files in the `metadata_templates/` folder:

#### abstract.txt
Write a summary of your RMSF dataset, similar to:
"This dataset contains monthly Rocky Mountain Spotted Fever (RMSF) case counts for Arizona counties from 2006 to 2021, along with associated climate variables, land cover data, and veterinary care access metrics. The data was compiled to investigate the relationships between environmental factors, veterinary care access, and RMSF incidence in Arizona."

#### methods.txt
Describe how the data was collected:
- RMSF case data source (Arizona Department of Health Services)
- Climate data sources (MODIS, GRIDMET)
- Land cover data source (National Land Cover Dataset)
- Data processing methods
- Quality control procedures

#### keywords.txt
Add relevant keywords (one per line):
```
Rocky Mountain Spotted Fever
RMSF
tick-borne disease
Arizona
climate change
veterinary care access
epidemiology
public health
```

#### personnel.txt
Fill out the table with your information:
- Your name as creator
- Yan as PI
- Daniel as contact
- Include email addresses and any ORCIDs

#### attributes_RMSF_Arizona_2006_2021.txt
For each column in your data, provide:
- attributeName: column name
- attributeDefinition: what the column contains
- class: numeric, character, Date, or factor
- unit: for numeric columns (e.g., celsius, millimeter, percent, count)
- dateTimeFormatString: for date columns (YYYY-MM-DD)
- missingValueCode: if applicable
- missingValueCodeExplanation: why data is missing

### 8. Generate the EML Metadata File
```r
make_eml(
    path = "./metadata_templates",
    data.path = "./data_objects",
    eml.path = "./eml",
    dataset.title = "Rocky Mountain Spotted Fever Incidence and Associated Environmental Factors in Arizona Counties, 2006-2021",
    temporal.coverage = c('2006-01-01', '2021-12-31'),
    geographic.description = "Counties in Arizona, USA with reported RMSF cases",
    geographic.coordinates = c(31.0, -114.0, 37.0, -109.0),  # Arizona bounding box
    maintenance.description = "completed",
    data.table = 'RMSF_Arizona_2006_2021.csv',
    data.table.name = 'RMSF Case and Environmental Data',
    data.table.description = 'Monthly RMSF case counts with associated climate, land cover, and veterinary care access data for Arizona counties',
    user.id = 'your_orcid',  # your ORCID if you have one
    user.domain = 'EDI',
    package.id = 'edi.XXX.1'  # Will be assigned by EDI
)
```

### 9. Review the Generated XML
Check the file created in the `eml/` folder. It should be an XML file that contains all your metadata in EML format.

### 10. Next Steps
1. Have Daniel review your metadata to ensure it's correct
2. Contact UNM EDI to get access and a package ID assigned
3. Upload your data package to EDI's staging environment for testing
4. Publish to the EDI repository

## Tips for Success
- Be thorough in your descriptions - the more detail, the better
- Use controlled vocabularies when possible (especially for keywords)
- Ensure all dates are in YYYY-MM-DD format
- Document any data processing or quality control steps
- Include funding information in the methods or additional_info

## Common Issues
- If you get errors about missing values, check that your CSV doesn't have "NA" strings
- Make sure all file paths are correct
- The CRAN version of EMLassemblyline is outdated - always use the GitHub version
- If templates don't generate properly, check that your data file is properly formatted UTF-8 CSV

## Resources
- EMLassemblyline documentation: https://ediorg.github.io/EMLassemblyline/
- EDI resources: https://edirepository.org/resources/creating-metadata-for-publication
- Example published dataset: https://portal.edirepository.org/nis/mapbrowse?packageid=edi.492.1