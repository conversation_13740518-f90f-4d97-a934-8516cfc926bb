# RMSF EDI Metadata Package

This directory contains the EDI-compliant metadata package for the Rocky Mountain Spotted Fever (RMSF) dataset from Arizona (2006-2021).

## Directory Structure

```
RMSF_EDI_metadata/
├── data_objects/          # Contains the CSV data file
│   └── RMSF_Arizona_data.csv
├── metadata_templates/    # Contains filled metadata templates
│   ├── abstract.txt
│   ├── additional_info.txt
│   ├── attributes_RMSF_Arizona_data.txt
│   ├── custom_units.txt
│   ├── geographic_coverage.txt
│   ├── intellectual_rights.txt
│   ├── keywords.txt
│   ├── methods.txt
│   └── personnel.txt
├── eml/                   # Will contain the generated EML XML file
├── create_eml.R          # R script to generate EML metadata
├── setup_templates.R     # R script to generate template files
└── README.md             # This file
```

## Data Description

The dataset contains daily RMSF case counts and associated variables for 13 Arizona counties from 2006-2021:
- **Temporal Coverage**: 2006-01-01 to 2021-12-31
- **Geographic Coverage**: Arizona, USA (13 counties)
- **Number of Records**: 2,496
- **Number of Variables**: 20

## Variables Included

1. **County**: Name of Arizona county
2. **Date**: Date of observation (YYYY-MM-DD)
3. **Cases**: Number of reported RMSF cases
4. **RMSF_IP**: RMSF incidence per 100,000 population
5. **LST**: Land surface temperature (°C)
6. **RH**: Relative humidity (%)
7. **Precipitation**: Daily precipitation (mm)
8. **NDVI**: Normalized Difference Vegetation Index
9. **Total_Pop**: Total county population
10. **Forest**: Forest coverage (%)
11. **Shrub**: Shrubland coverage (%)
12. **VA**: Veterinary care access (clinics per 10,000 population)
13. **DogPop**: Estimated dog population
14. **RPL_THEME1-4**: Social Vulnerability Index theme rankings
15. **RPL_THEMES**: Overall SVI ranking
16. **SVI**: Social Vulnerability Index value
17. **RPL_Mean**: Mean of SVI theme rankings

## How to Generate the EML Metadata

1. Install required R package:
```r
remotes::install_github("EDIorg/EMLassemblyline")
```

2. Run the create_eml.R script:
```r
source("create_eml.R")
```

3. The EML XML file will be generated in the `eml/` directory

## Next Steps

1. Have Daniel review the metadata to ensure correctness
2. Contact UNM EDI to get an official package ID assigned
3. Replace the placeholder package ID (edi.999.1) with the assigned ID
4. Upload to EDI's staging environment for testing
5. Publish to the EDI repository

## Notes

- All metadata templates have been filled based on the data structure and RMSF research context
- Custom units were defined for incidence rates (per 100,000 and per 10,000 population)
- The intellectual rights use CC-BY license as recommended
- Personnel information includes creator, PI, and contact person