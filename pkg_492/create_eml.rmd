---
title: "make_eml"
author: "dbeene"
date: "12/3/2019"
output: html_document
---

```{r setup, include=FALSE}
# Initialize .Rmd

knitr::opts_chunk$set(echo = TRUE)
library(kableExtra)
library(EMLassemblyline)
library(XML)

```

Data package has to be in the following format:
data_objects <- this is where all tables and other data are stored
eml <- this is where the eml document will be output
metadata_templates <- text files used to describe data package, defined in 'data.table.description' argument

```{r}
# Make EML
make_eml(
  path = './metadata_templates',
  data.path = './data_objects',
  eml.path = './eml',
  dataset.title = 'Uranium mobility and accumulation along the Rio Paguate, Jackpile Mine in Laguna Pueblo, NM',
  temporal.coverage = c('2014-09-17', '2016-08-18'),
  maintenance.description = 'completed',
  data.table = c('EP100101_sites.csv', 'EP100102_water.csv', 'EP100103_hyporheic_zone.csv', 'EP100104_microbes.csv', 'EP100105_weather.csv', 'EP100106_xrd.csv'),
  data.table.description = c('EP100101_attributes_sites', 'EP100102_attributes_water', 'EP100103_attributes_hyporheic_zone', 'EP100104_attributes_microbes', 'EP100105_attributes_weather', 'EP100106_attributes_xrd'),
  other.entity = c('SRPEP1001.tar'),
  other.entity.description = c('SQL database'),
  user.id = 'dbeene',
  user.domain = 'EDI',
  package.id = 'edi.301.2'
)


```
